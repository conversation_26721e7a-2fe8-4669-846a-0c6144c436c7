#!/usr/bin/env python3
"""
Test script for the Grid Bot Adventure MCP Server
"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_server():
    """Test the MCP server functionality"""
    try:
        # Import the server
        from grid_game_server import app
        print("✅ Successfully imported grid_game_server")
        
        # Test that we can access the tools
        tools = await app.list_tools()
        print(f"✅ Found {len(tools)} tools:")
        for tool in tools:
            print(f"  - {tool.name}: {tool.description}")
        
        # Test starting a new game
        print("\n🎮 Testing game initialization...")
        result = await app.call_tool("start_new_game", {})
        print("✅ Game started successfully!")
        print(result[:200] + "..." if len(result) > 200 else result)
        
        # Test moving the bot
        print("\n🤖 Testing bot movement...")
        move_result = await app.call_tool("move_bot", {"direction": "north"})
        print("✅ Bot moved successfully!")
        print(move_result[:200] + "..." if len(move_result) > 200 else move_result)
        
        # Test getting game state
        print("\n📊 Testing game state...")
        state_result = await app.call_tool("get_game_state", {})
        print("✅ Game state retrieved successfully!")
        print(state_result[:200] + "..." if len(state_result) > 200 else state_result)
        
        print("\n🎉 All tests passed! MCP server is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_server())
    sys.exit(0 if success else 1)
