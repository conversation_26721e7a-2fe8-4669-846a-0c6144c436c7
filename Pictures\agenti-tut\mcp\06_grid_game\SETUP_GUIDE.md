# 🎮 Grid Bot Adventure - Claude Desktop Setup Guide

## ✅ **UNICODE ERROR FIXED!**

The Unicode encoding error has been resolved. Here's how to set up the game with <PERSON>:

## 🔧 **Setup Instructions**

### **Option 1: Use the Fixed Launcher (Recommended)**

1. **Copy this configuration** to your Claude Desktop settings:

```json
{
  "mcpServers": {
    "grid-bot-adventure": {
      "command": "python",
      "args": [
        "C:\\Users\\<USER>\\Pictures\\agenti-tut\\mcp\\06_grid_game\\start_server.py"
      ],
      "env": {
        "PYTHONIOENCODING": "utf-8"
      }
    }
  }
}
```

### **Option 2: Use Direct Server (Also Fixed)**

```json
{
  "mcpServers": {
    "grid-bot-adventure": {
      "command": "python",
      "args": [
        "C:\\Users\\<USER>\\Pictures\\agenti-tut\\mcp\\06_grid_game\\grid_game_server.py"
      ],
      "env": {
        "PYTHONIOENCODING": "utf-8"
      }
    }
  }
}
```

## 📁 **Configuration File Location**

Your Claude Desktop config file is located at:
```
C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\saoudrizwan.claude-dev\settings\cline_mcp_settings.json
```

## 🚀 **How to Apply the Fix**

1. **Open your config file** (the one you showed me)
2. **Replace the entire content** with one of the configurations above
3. **Save the file**
4. **Restart Claude Desktop**

## 🎯 **What Was Fixed**

### **The Problem:**
- Windows console couldn't handle Unicode emojis in print statements
- Error: `UnicodeEncodeError: 'charmap' codec can't encode character`

### **The Solution:**
- Added UTF-8 encoding setup for Windows systems
- Created fallback handling for encoding issues
- Removed problematic emoji characters from startup messages
- Added environment variable `PYTHONIOENCODING: utf-8`

## 🧪 **Test the Setup**

After applying the configuration:

1. **Restart Claude Desktop**
2. **Try these commands:**

```
Start a new grid bot adventure game
```

```
Move the bot north and analyze the situation
```

```
Fire laser east to destroy enemies
```

```
What's the best strategy to collect all batteries safely?
```

## 🎮 **Available MCP Tools**

Once connected, you'll have access to:

- **`start_new_game()`** - Initialize new game
- **`move_bot(direction)`** - Move north/south/east/west  
- **`fire_laser(direction)`** - Shoot laser weapon
- **`get_game_state()`** - Current game status
- **`look_around()`** - Examine surroundings
- **`analyze_threats()`** - Strategic analysis
- **`find_nearest_coin()`** - Locate batteries
- **`get_game_help()`** - Game instructions

## 🔥 **Game Features**

- **🤖 Strategic Bot Control** - AI navigates grid-based world
- **🔋 Battery Collection** - Collect batteries to power up (10 pts each)
- **🏆 Golden Batteries** - Special high-value batteries (50 pts each)
- **🐢 Enemy Avoidance** - 3 types of turtle enemies with different AI
- **⚡ Laser Weapon** - Destroy enemies with directional laser
- **❤️ Health System** - Bot can take 3 hits before game over
- **🎮 Strategic Gameplay** - Risk vs reward decision making

## 🌟 **Pro Tips for AI Gaming**

### **Strategic Commands:**
```
"Analyze the current threats and plan a safe route to the nearest battery"

"I'm at 1 health - what's the safest strategy to collect remaining batteries?"

"Use the laser weapon to clear a path to the golden batteries"

"Plan a 5-move sequence to maximize score while avoiding enemies"
```

### **Advanced Gameplay:**
```
"What's the optimal balance between collecting batteries and eliminating threats?"

"Predict enemy movement patterns and plan accordingly"

"Calculate the most efficient route to collect all batteries"
```

## 🐛 **Troubleshooting**

### **If the server still won't start:**

1. **Check Python installation:**
   ```bash
   python --version
   ```

2. **Test the server manually:**
   ```bash
   cd C:\Users\<USER>\Pictures\agenti-tut\mcp\06_grid_game
   python grid_game_server.py
   ```

3. **Check MCP package:**
   ```bash
   pip install mcp
   ```

### **If tools don't appear in Claude:**

1. **Verify the file path** in your config
2. **Restart Claude Desktop completely**
3. **Check for typos** in the JSON configuration
4. **Ensure proper JSON formatting** (use a JSON validator)

## 🎉 **Success Indicators**

You'll know it's working when:
- ✅ No Unicode errors in Claude Desktop
- ✅ Grid game tools appear in Claude's tool list
- ✅ You can start a new game and see the grid
- ✅ Bot responds to movement commands
- ✅ Laser weapon functions properly

## 🚀 **Ready to Play!**

Once set up, you'll have a **fully functional AI-controlled strategic game** that demonstrates:

- **Advanced AI Decision Making**
- **Spatial Reasoning and Navigation** 
- **Risk Assessment and Planning**
- **Resource Management**
- **Combat Strategy**

**The Unicode error is completely fixed - your game should work perfectly now! 🎮✨**
