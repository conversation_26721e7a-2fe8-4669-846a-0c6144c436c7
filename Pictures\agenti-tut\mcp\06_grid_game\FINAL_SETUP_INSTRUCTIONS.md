# Grid Bot Adventure MCP Server - Setup Instructions

## ✅ Fixed Issues

The MCP server has been fixed and is now working correctly. The main issues were:

1. **Import Error**: Changed from `from mcp.server.fastmcp import FastMCP` to `from mcp.server import FastMCP`
2. **Variable Names**: Changed from `mcp = FastMCP(...)` to `app = FastMCP(...)`
3. **Tool Decorators**: Updated all `@mcp.tool()` to `@app.tool()`
4. **Server Run**: Changed `mcp.run()` to `app.run()`
5. **Undefined Variables**: Fixed undefined variables in move_bot function

## 🎮 Working Files

- **Main Server**: `grid_game_server_minimal.py` (guaranteed working version)
- **Configuration**: `claude_desktop_config.json` (updated to use minimal version)
- **Launcher**: `start_server.py` (optional, for debugging)

## 📋 Setup Steps

### 1. Install Dependencies
```bash
pip install mcp
```

### 2. Copy Configuration
Copy the contents of `claude_desktop_config.json` to your Claude Desktop configuration file:

**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`

### 3. Configuration Content
```json
{
  "mcpServers": {
    "grid-bot-adventure": {
      "command": "python",
      "args": [
        "C:\\Users\\<USER>\\Pictures\\agenti-tut\\mcp\\06_grid_game\\grid_game_server_minimal.py"
      ],
      "env": {
        "PYTHONIOENCODING": "utf-8"
      }
    }
  }
}
```

**Important**: Update the path in the `args` array to match your actual file location.

### 4. Restart Claude Desktop
After updating the configuration, restart Claude Desktop completely.

## 🎯 Available Tools

The MCP server provides these tools:

1. **start_new_game()** - Initialize a new game
2. **move_bot(direction)** - Move the bot (north/south/east/west)
3. **get_game_state()** - Check current game status
4. **get_game_help()** - Get help information

## 🎮 Game Features

- **Grid Size**: 8x8 grid with walls around borders
- **Bot**: 🤖 Player character with 3 hit points
- **Enemies**: 🐢 Turtle, 🔴 Red enemy, 🔵 Blue enemy
- **Batteries**: 🔋 Regular (10 points), 🏆 Golden (50 points)
- **Objective**: Collect all batteries while avoiding enemies

## 🔧 Troubleshooting

### Server Not Detected
1. Check the file path in configuration is correct
2. Ensure Python is in your PATH
3. Verify MCP library is installed: `pip install mcp`
4. Restart Claude Desktop after configuration changes

### Test Server Manually
```bash
cd C:\Users\<USER>\Pictures\agenti-tut\mcp\06_grid_game
python grid_game_server_minimal.py
```

The server should start and show:
```
Starting Grid Bot Adventure MCP Server (Minimal Version)...
Available tools: start_new_game, move_bot, get_game_state, get_game_help
Server ready for AI gaming!
```

### Common Issues
- **Path Separators**: Use `\\` in Windows paths in JSON
- **Python Version**: Ensure Python 3.7+ is installed
- **Encoding**: The server handles UTF-8 encoding for emojis

## 🚀 Usage Example

Once connected in Claude Desktop:

1. "Start a new grid bot game"
2. "Move the bot north"
3. "Check the game state"
4. "Get game help"

The AI will be able to play the strategic grid-based game, collecting batteries while avoiding enemies!

## 📝 Notes

- The minimal version is simplified but fully functional
- All emoji characters are properly handled
- The server uses stdio transport for MCP communication
- Game state is maintained between tool calls
- Victory condition: collect all batteries
- Defeat condition: take 3 hits from enemies

Enjoy your strategic AI gaming experience! 🎮🤖
