#!/usr/bin/env python3
"""
Demo script showing the Grid Bot Adventure game in action
"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def demo_game():
    """Demonstrate the game functionality"""
    try:
        # Import the server
        from grid_game_server import app
        print("🤖 Grid Bot Adventure Demo")
        print("=" * 50)
        
        # Start a new game
        print("\n🎮 Starting new game...")
        result = await app.call_tool("start_new_game", {})
        print(result[0].text)
        
        # Look around first
        print("\n🔍 Looking around...")
        look_result = await app.call_tool("look_around", {})
        print(look_result[0].text)
        
        # Move the bot a few times
        moves = ["north", "east", "south", "west"]
        for i, direction in enumerate(moves):
            print(f"\n🤖 Move {i+1}: Moving {direction}...")
            move_result = await app.call_tool("move_bot", {"direction": direction})
            print(move_result[0].text)
            
            # Show current state after each move
            state_result = await app.call_tool("get_game_state", {})
            # Just show the grid part
            state_text = state_result[0].text
            grid_start = state_text.find("🗺️ Game Grid:")
            if grid_start != -1:
                grid_part = state_text[grid_start:grid_start+500]
                print(grid_part)
            
            print("-" * 30)
        
        print("\n🎉 Demo completed! The MCP server is working correctly.")
        print("You can now use this server with your MCP client.")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(demo_game())
