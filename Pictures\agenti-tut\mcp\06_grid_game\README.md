# 🤖 Grid Bot Adventure - MCP Game

A strategic grid-based game where an LLM controls a bot to collect coins while avoiding turtle enemies!

## 🎯 Game Overview

**"Coin Collector Bot"** - A turn-based strategy game featuring:
- 🤖 Player bot that moves on a grid
- 🐢 Turtle enemies that patrol and chase
- 🪙 Coins to collect for points
- 🧠 Strategic gameplay requiring planning and adaptation

## 🎮 Game Mechanics

### Player Bot 🤖
- Moves one grid cell at a time (up, down, left, right)
- Collects coins by moving onto them
- Dies if touched by a turtle enemy
- Goal: Collect all coins without dying

### Turtle Enemies 🐢
- Move randomly or chase the player
- Different turtle types with different behaviors:
  - **Green Turtle**: Moves randomly
  - **Red Turtle**: Chases player when nearby
  - **Blue Turtle**: Patrols in patterns

### Coins 🪙
- Scattered randomly across the grid
- Each coin gives 10 points
- Some special golden coins give 50 points
- Level complete when all coins collected

### Grid World 🗺️
- 10x10 grid battlefield
- Walls and obstacles block movement
- Safe zones where turtles can't enter
- Power-ups that give temporary abilities

## 🚀 Quick Start

### 1. Install Dependencies
```bash
cd mcp/06_grid_game
pip install mcp fastapi uvicorn websockets
```

### 2. Start the Game Server
```bash
python grid_game_server.py
```

### 3. View the Game (Optional)
```bash
# In another terminal
python web_server.py
# Open http://localhost:8081/game
```

### 4. Connect to Claude Desktop
```json
{
  "mcpServers": {
    "grid-game": {
      "command": "python",
      "args": ["/absolute/path/to/grid_game_server.py"]
    }
  }
}
```

### 5. Let the LLM Play!
Try these commands in Claude:
- "Start a new grid game"
- "Look around and analyze the current situation"
- "Move the bot north to avoid the turtle"
- "What's the safest path to collect coins?"
- "Get the current game state"

## 🔧 MCP Tools Available

### Game Control:
- `start_new_game()` - Initialize a fresh game
- `move_bot(direction)` - Move bot (north/south/east/west)
- `get_game_state()` - Current game status and grid
- `look_around()` - Detailed view of surroundings

### Strategy Tools:
- `analyze_threats()` - Identify nearby enemies and dangers
- `find_nearest_coin()` - Locate closest collectible coin
- `calculate_safe_path(target_x, target_y)` - Plan safe route
- `get_game_stats()` - Score, coins collected, moves made

### Advanced Features:
- `use_power_up(type)` - Activate special abilities
- `predict_enemy_moves()` - Forecast turtle movements
- `get_optimal_strategy()` - AI-suggested best moves

## 🎯 Educational Value

### For Students:
- **Game Development**: P5.js grid-based game programming
- **AI Strategy**: How LLMs approach tactical games
- **Pathfinding**: Safe route calculation algorithms
- **State Management**: Game state tracking and updates

### For LLMs:
- **Spatial Reasoning**: Understanding 2D grid navigation
- **Risk Assessment**: Evaluating danger vs reward
- **Strategic Planning**: Multi-step move sequences
- **Pattern Recognition**: Learning enemy behavior patterns

## 🎮 Game Features

### Visual Elements:
- **Emoji Graphics**: 🤖 bot, 🐢 turtles, 🪙 coins
- **Grid Display**: Clear ASCII art representation
- **Real-time Updates**: Live game state visualization
- **Color Coding**: Different elements easily distinguishable

### Difficulty Levels:
- **Easy**: Few enemies, many coins, large safe zones
- **Medium**: Balanced challenge with smart enemies
- **Hard**: Many fast enemies, few coins, complex maze
- **Expert**: Advanced AI enemies with coordinated behavior

## 🧠 AI Strategy Examples

### Basic Strategy:
1. **Scan Environment** → Identify threats and opportunities
2. **Plan Route** → Calculate safest path to nearest coin
3. **Execute Move** → Move one step toward goal
4. **Reassess** → Check for new threats after each move

### Advanced Strategy:
1. **Threat Analysis** → Predict enemy movement patterns
2. **Risk/Reward** → Evaluate coin value vs danger level
3. **Tactical Positioning** → Use walls and safe zones strategically
4. **Adaptive Planning** → Adjust strategy based on enemy behavior

## 🎨 Game Variants

### Classic Mode:
- Standard grid with random coin placement
- Mixed turtle types with different behaviors
- Progressive difficulty as game continues

### Maze Mode:
- Complex maze layout with walls and corridors
- Turtles patrol specific routes
- Hidden coins in secret areas

### Time Attack:
- Collect coins before time runs out
- Faster movement but more aggressive enemies
- Bonus points for speed completion

### Puzzle Mode:
- Specific coin collection sequences required
- Enemies follow predictable patterns
- Logic-based solutions needed

## 🏆 Scoring System

### Points:
- **Regular Coin**: 10 points
- **Golden Coin**: 50 points
- **Speed Bonus**: Extra points for quick completion
- **Efficiency Bonus**: Fewer moves = higher score

### Achievements:
- 🏃 **Speed Runner**: Complete level in under 20 moves
- 🧠 **Strategist**: Complete without backtracking
- 💰 **Collector**: Find all hidden golden coins
- 🛡️ **Survivor**: Complete without using safe zones

---

**This demonstrates advanced AI-game interaction - LLMs playing strategic games with real-time decision making! 🚀🎮**
