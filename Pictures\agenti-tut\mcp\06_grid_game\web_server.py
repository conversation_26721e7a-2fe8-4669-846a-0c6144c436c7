#!/usr/bin/env python3
"""
🌐 Grid Bot Adventure Web Server

Simple web server to host the P5.js game interface.
This allows you to see the visual representation of the grid game
while the LLM controls it through MCP.
"""

import http.server
import socketserver
import webbrowser
import os
import threading
import time

PORT = 8081

class GameHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom HTTP request handler for the game"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.path.dirname(os.path.abspath(__file__)), **kwargs)
    
    def end_headers(self):
        # Add CORS headers for development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def log_message(self, format, *args):
        # Custom logging
        print(f"🌐 {self.address_string()} - {format % args}")

def start_web_server():
    """Start the web server"""
    try:
        with socketserver.TCPServer(("", PORT), GameHTTPRequestHandler) as httpd:
            print(f"🌐 Grid Bot Adventure Web Server")
            print(f"🚀 Server starting on http://localhost:{PORT}")
            print(f"🎮 Game available at: http://localhost:{PORT}/game.html")
            print(f"📖 Documentation at: http://localhost:{PORT}/README.md")
            print("🛑 Press Ctrl+C to stop the server")
            print()
            
            # Auto-open browser after a short delay
            def open_browser():
                time.sleep(2)
                try:
                    webbrowser.open(f"http://localhost:{PORT}/game.html")
                    print(f"🌐 Opened game in your default browser!")
                except:
                    print(f"💡 Manually open: http://localhost:{PORT}/game.html")
            
            browser_thread = threading.Thread(target=open_browser, daemon=True)
            browser_thread.start()
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except OSError as e:
        if e.errno == 10048:  # Port already in use
            print(f"❌ Port {PORT} is already in use!")
            print(f"💡 Try a different port or stop the existing server")
        else:
            print(f"❌ Server error: {e}")

def main():
    """Main function"""
    print("🤖 Grid Bot Adventure - Web Interface")
    print("=" * 45)
    print("This starts a web server to view the game visually")
    print("while an LLM controls it through MCP!")
    print()
    
    # Check if game.html exists
    game_file = os.path.join(os.path.dirname(__file__), "game.html")
    if not os.path.exists(game_file):
        print("❌ game.html not found!")
        print("💡 Make sure you're running this from the correct directory")
        return
    
    print("📁 Files found:")
    for file in ["game.html", "README.md", "grid_game_server.py"]:
        if os.path.exists(os.path.join(os.path.dirname(__file__), file)):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} (missing)")
    
    print()
    start_web_server()

if __name__ == "__main__":
    main()
